class AudioRecorder {
    constructor() {
        this.mediaRecorder = null;
        this.audioBuffer = [];
        this.stream = null;
        this.isRecording = false;
        this.isSending = false;
        
        this.BUFFER_DURATION = 15000; // 15 seconds
        this.CHUNK_DURATION = 1000; // 1 second chunks
        this.MAX_CHUNKS = 15; // 15 chunks of 1 second each
        // Old API_URL removed - only streaming mode supported
        
        // Add audio processing context for continuous buffer
        this.bufferAudioContext = null;
        this.bufferSource = null;
        this.bufferProcessor = null;
        this.rawAudioBuffer = []; // Store raw PCM data instead of encoded chunks
        this.sampleRate = 44100;

        // Streaming mode properties (silence detection removed)
        this.isStreaming = false; // Track if currently streaming to assistant

        this.recordBtn = document.getElementById('recordBtn');
        this.wakeWordBtn = document.getElementById('wakeWordBtn');
        this.status = document.getElementById('status');
        this.userTranscriptDiv = document.getElementById('userTranscript');
        this.transcriptDiv = document.getElementById('transcript');
        this.wakeWordIndicator = document.getElementById('wakeWordIndicator');
        this.wakeWordStatus = document.getElementById('wakeWordStatus');
        
        // Old batch processing removed - only streaming mode supported
        this.recordBtn.style.display = 'none'; // Hide the old record button
        this.wakeWordBtn.addEventListener('click', () => this.toggleWakeWordListening());
        
        // Wake word detection properties
        this.websocket = null;
        this.wakeWordEnabled = false;
        this.audioContext = null;
        this.scriptProcessor = null;

        // Real-time streaming properties
        this.realtimeWebsocket = null;
        this.streamingAudioContext = null;
        this.streamingProcessor = null;
        this.streamingSource = null;
        this.audioPlaybackQueue = [];
        this.isPlayingAudio = false;
        this.audioPlaybackContext = null;
        this.responseComplete = false;
        this.shouldStopAfterAudio = false;
        this.nextStartTime = 0;
        this.activeSources = [];

        // Typing sound properties
        this.typingSound = null;
        this.isTypingSoundPlaying = false;
        
        this.checkBrowserSupport();
        this.startContinuousRecording();
        // Don't initialize wake word detection automatically - wait for user interaction
    }
    
    checkBrowserSupport() {
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            this.showError('Browser not supported. Please use a modern browser.');
            this.recordBtn.disabled = true;
            return false;
        }
        return true;
    }
    
    async startContinuousRecording() {
        try {
            this.stream = await navigator.mediaDevices.getUserMedia({ 
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    sampleRate: this.sampleRate
                } 
            });
            
            // Initialize audio context for raw audio capture
            this.bufferAudioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: this.sampleRate
            });

            // Create source from microphone
            this.bufferSource = this.bufferAudioContext.createMediaStreamSource(this.stream);

            // Create script processor for raw audio capture
            const bufferSize = 4096; // Larger buffer for efficiency
            this.bufferProcessor = this.bufferAudioContext.createScriptProcessor(bufferSize, 1, 1);

            this.bufferProcessor.onaudioprocess = (event) => {
                if (this.isRecording) {
            const inputData = event.inputBuffer.getChannelData(0);
                    // Store copy of the audio data
                    this.addToRawBuffer(new Float32Array(inputData));
                }
            };
            
            // Connect the audio graph
            this.bufferSource.connect(this.bufferProcessor);
            this.bufferProcessor.connect(this.bufferAudioContext.destination);

            this.isRecording = true;
            
            this.recordBtn.textContent = 'Send Last 60 Seconds';
            this.recordBtn.disabled = false;
            this.status.textContent = 'Continuously recording... Click button to send last 60 seconds.';
        } catch (error) {
            console.error('Error starting continuous recording:', error);
            this.showError(`Error: ${error.message}`);
        }
    }

    addToRawBuffer(audioData) {
        // Add timestamp to chunk
        const timestampedChunk = {
            data: audioData,
            timestamp: Date.now()
        };
        
        this.rawAudioBuffer.push(timestampedChunk);

        // Wake word buffer no longer needed in streaming mode
        // Silence detection removed - using streaming mode only

        // Remove chunks older than 15 seconds
        const cutoffTime = Date.now() - this.BUFFER_DURATION;
        this.rawAudioBuffer = this.rawAudioBuffer.filter(chunk => chunk.timestamp > cutoffTime);

        // Calculate total samples and limit if necessary
        const totalSamples = this.rawAudioBuffer.reduce((sum, chunk) => sum + chunk.data.length, 0);
        const maxSamples = this.sampleRate * 15; // 15 seconds worth of samples

        if (totalSamples > maxSamples) {
            // Remove oldest chunks until we're under the limit
            while (this.rawAudioBuffer.length > 0 &&
                   this.rawAudioBuffer.reduce((sum, chunk) => sum + chunk.data.length, 0) > maxSamples) {
                this.rawAudioBuffer.shift();
            }
        }
    }

    extractPreWakeWordBuffer() {
        /**
         * Extract the current 15-second audio buffer and convert it to WAV format
         * Returns base64 encoded WAV data suitable for sending to backend
         */
        if (this.rawAudioBuffer.length === 0) {
            return null;
        }

        // Combine all audio chunks into a single Float32Array
        const totalSamples = this.rawAudioBuffer.reduce((sum, chunk) => sum + chunk.data.length, 0);
        const combinedAudio = new Float32Array(totalSamples);

        let offset = 0;
        for (const chunk of this.rawAudioBuffer) {
            combinedAudio.set(chunk.data, offset);
            offset += chunk.data.length;
        }

        // Convert Float32 to Int16 (PCM16)
        const pcm16Data = new Int16Array(combinedAudio.length);
        for (let i = 0; i < combinedAudio.length; i++) {
            // Clamp to [-1, 1] and convert to 16-bit signed integer
            const sample = Math.max(-1, Math.min(1, combinedAudio[i]));
            pcm16Data[i] = sample * 32767;
        }

        // Create WAV header
        const sampleRate = this.sampleRate;
        const numChannels = 1; // Mono
        const bitsPerSample = 16;
        const byteRate = sampleRate * numChannels * bitsPerSample / 8;
        const blockAlign = numChannels * bitsPerSample / 8;
        const dataSize = pcm16Data.length * 2; // 2 bytes per sample
        const fileSize = 36 + dataSize;

        // Create WAV file buffer
        const buffer = new ArrayBuffer(44 + dataSize);
        const view = new DataView(buffer);

        // WAV header
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };

        writeString(0, 'RIFF');
        view.setUint32(4, fileSize, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true); // PCM format chunk size
        view.setUint16(20, 1, true); // PCM format
        view.setUint16(22, numChannels, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, byteRate, true);
        view.setUint16(32, blockAlign, true);
        view.setUint16(34, bitsPerSample, true);
        writeString(36, 'data');
        view.setUint32(40, dataSize, true);

        // Write PCM data
        for (let i = 0; i < pcm16Data.length; i++) {
            view.setInt16(44 + i * 2, pcm16Data[i], true);
        }

        // Convert to base64
        const uint8Array = new Uint8Array(buffer);
        let binaryString = '';
        for (let i = 0; i < uint8Array.length; i++) {
            binaryString += String.fromCharCode(uint8Array[i]);
        }

        return btoa(binaryString);
    }

    // Silence detection methods removed - using streaming mode only

    // Old batch processing method removed - only streaming mode supported

    createWavFromRawData(floatData, sampleRate) {
        const length = floatData.length;
        const buffer = new ArrayBuffer(44 + length * 2);
        const view = new DataView(buffer);

        // WAV header
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };

        // RIFF chunk descriptor
        writeString(0, 'RIFF');
        view.setUint32(4, 36 + length * 2, true);
        writeString(8, 'WAVE');

        // fmt sub-chunk
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true); // Subchunk1Size
        view.setUint16(20, 1, true); // AudioFormat (PCM)
        view.setUint16(22, 1, true); // NumChannels
        view.setUint32(24, sampleRate, true); // SampleRate
        view.setUint32(28, sampleRate * 2, true); // ByteRate
        view.setUint16(32, 2, true); // BlockAlign
        view.setUint16(34, 16, true); // BitsPerSample

        // data sub-chunk
        writeString(36, 'data');
        view.setUint32(40, length * 2, true);

        // Convert float samples to 16-bit PCM
        let offset = 44;
        for (let i = 0; i < length; i++) {
            const sample = Math.max(-1, Math.min(1, floatData[i]));
            view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
            offset += 2;
        }

        return new Blob([buffer], { type: 'audio/wav' });
    }

    createWavFromPCM16(pcm16Data, sampleRate) {
        const length = pcm16Data.length;
        const buffer = new ArrayBuffer(44 + length * 2);
        const view = new DataView(buffer);

        // WAV header
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };

        // RIFF chunk descriptor
        writeString(0, 'RIFF');
        view.setUint32(4, 36 + length * 2, true);
        writeString(8, 'WAVE');

        // fmt sub-chunk
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true); // Subchunk1Size
        view.setUint16(20, 1, true); // AudioFormat (PCM)
        view.setUint16(22, 1, true); // NumChannels
        view.setUint32(24, sampleRate, true); // SampleRate
        view.setUint32(28, sampleRate * 2, true); // ByteRate
        view.setUint16(32, 2, true); // BlockAlign
        view.setUint16(34, 16, true); // BitsPerSample

        // data sub-chunk
        writeString(36, 'data');
        view.setUint32(40, length * 2, true);

        // Copy PCM16 data directly (already in correct format)
        let offset = 44;
        for (let i = 0; i < length; i++) {
            view.setInt16(offset, pcm16Data[i], true);
            offset += 2;
        }

        return new Blob([buffer], { type: 'audio/wav' });
    }

    // Old batch processing methods removed - only streaming mode supported
    
    getSupportedMimeType() {
        const types = [
            'audio/webm;codecs=opus',
            'audio/webm',
            'audio/ogg;codecs=opus',
            'audio/mp4'
        ];
        
        for (const type of types) {
            if (MediaRecorder.isTypeSupported(type)) {
                return type;
            }
        }
        
        return 'audio/webm';
    }
    
    showError(message) {
        this.status.textContent = message;
        this.status.classList.add('error');
        setTimeout(() => {
            this.status.classList.remove('error');
        }, 5000);
    }

    // Typing sound management methods
    startTypingSound() {
        if (this.isTypingSoundPlaying) {
            return; // Already playing
        }

        try {
            // Create new audio element if it doesn't exist
            if (!this.typingSound) {
                this.typingSound = new Audio('Typing.wav');
                this.typingSound.loop = true;
                this.typingSound.volume = 0.3; // Set to moderate volume
            }

            this.typingSound.currentTime = 0; // Reset to beginning
            this.typingSound.play();
            this.isTypingSoundPlaying = true;

            console.log('Started typing sound');
        } catch (error) {
            console.error('Error starting typing sound:', error);
        }
    }

    stopTypingSound() {
        if (!this.isTypingSoundPlaying || !this.typingSound) {
            return; // Not playing or doesn't exist
        }

        try {
            this.typingSound.pause();
            this.typingSound.currentTime = 0; // Reset to beginning for next time
            this.isTypingSoundPlaying = false;

            console.log('Stopped typing sound');
        } catch (error) {
            console.error('Error stopping typing sound:', error);
        }
    }
    
    hideResponses() {
        this.userTranscriptDiv.style.display = 'none';
        this.transcriptDiv.style.display = 'none';
        this.userTranscriptDiv.textContent = '';
        this.transcriptDiv.textContent = '';
        // Stop typing sound when hiding responses (e.g., starting new recording)
        this.stopTypingSound();
    }
    
    // Wake word detection methods
    async initializeWakeWordDetection() {
        // Remove the early return check to allow initialization

        try {
            // Initialize WebSocket connection
            await this.connectWebSocket();

            // Get microphone access for wake word detection
            // Use the existing stream if available, otherwise get new access
            if (!this.stream) {
                this.stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        sampleRate: 16000 // openWakeWord expects 16kHz audio
                    }
                });
            }

            // Initialize audio context for processing
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: 16000 // openWakeWord expects 16kHz audio
            });

            // Resume audio context if it's suspended (Chrome autoplay policy)
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }

            // Create audio processing pipeline
            await this.setupAudioProcessing();

        } catch (error) {
            console.error('Error initializing wake word detection:', error);
            this.showError('Wake word detection unavailable');
        }
    }
    
    connectWebSocket() {
        return new Promise((resolve, reject) => {
            const wsUrl = 'ws://localhost:8000/ws/wakeword';
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('WebSocket connected for wake word detection');
                this.updateWakeWordStatus('Listening for "Scarlett"...');
                resolve();
            };
            
            this.websocket.onmessage = (event) => {
                const message = JSON.parse(event.data);

                if (message.type === 'wakeword_detected') {
                    console.log('Wake word detected!', message);
                    this.handleWakeWordDetected();
                }
            };
            
            this.websocket.onclose = () => {
                console.log('WebSocket disconnected');
                this.updateWakeWordStatus('Wake word detection offline');
                
                // Attempt to reconnect after 3 seconds
                setTimeout(() => {
                    if (this.wakeWordEnabled) {
                        this.connectWebSocket();
                    }
                }, 3000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                reject(error);
            };
            
            // Keep connection alive with periodic pings
            setInterval(() => {
                if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                    this.websocket.send(JSON.stringify({ type: 'ping' }));
                }
            }, 30000); // Ping every 30 seconds
        });
    }
    
    async setupAudioProcessing() {
        // Create source from microphone stream
        const source = this.audioContext.createMediaStreamSource(this.stream);
        
        // Create script processor for capturing audio chunks
        // Buffer size of 512 samples at 16kHz = 32ms chunks
        this.scriptProcessor = this.audioContext.createScriptProcessor(512, 1, 1);
        
        this.scriptProcessor.onaudioprocess = (event) => {
            if (!this.wakeWordEnabled || !this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
                return;
            }
            
            // Get audio data
            const inputData = event.inputBuffer.getChannelData(0);
            
            // Convert float32 to int16
            const int16Data = new Int16Array(inputData.length);
            for (let i = 0; i < inputData.length; i++) {
                const s = Math.max(-1, Math.min(1, inputData[i]));
                int16Data[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
            }
            
            // Convert to base64 and send
            const base64Data = btoa(String.fromCharCode.apply(null, new Uint8Array(int16Data.buffer)));
            
            this.websocket.send(JSON.stringify({
                type: 'audio',
                data: base64Data
            }));
        };
        
        // Connect audio nodes
        source.connect(this.scriptProcessor);
        this.scriptProcessor.connect(this.audioContext.destination);
    }
    
    async handleWakeWordDetected() {
        if (this.isStreaming)
            return
        console.log('Wake word detected! Starting real-time streaming...');

        // Flash UI to indicate detection
        this.updateWakeWordStatus('Wake word detected! Please speak...');

        // Play tune to indicate processing
        const audio = new Audio('tune.wav');
        audio.volume = 1.0;
        audio.play();

        // Stop tune after 1 second with fade out
        setTimeout(() => {
            const fadeInterval = setInterval(() => {
                if (audio.volume > 0.1) {
                    audio.volume -= 0.1;
                } else {
                    clearInterval(fadeInterval);
                    audio.pause();
                    audio.currentTime = 0;
                    audio.volume = 1.0;
                }
            }, 25);
        }, 250);

        try {
            // Initialize real-time streaming
            await this.initializeRealtimeStreaming();

            // Wait for session to start
            await new Promise((resolve) => {
                const checkStreaming = () => {
                    if (this.isStreaming) {
                        resolve();
                    } else {
                        setTimeout(checkStreaming, 100);
                    }
                };
                checkStreaming();
            });

            // Extract and send the 15-second pre-wake-word buffer
            const preWakeWordBuffer = this.extractPreWakeWordBuffer();
            if (preWakeWordBuffer) {
                console.log('Sending 15-second pre-wake-word buffer to assistant...');
                this.realtimeWebsocket.send(JSON.stringify({
                    type: 'pre_wakeword_buffer',
                    data: preWakeWordBuffer
                }));

                // Wait for confirmation that buffer was added
                await new Promise((resolve) => {
                    const originalHandler = this.realtimeWebsocket.onmessage;
                    this.realtimeWebsocket.onmessage = (event) => {
                        const message = JSON.parse(event.data);
                        if (message.type === 'pre_wakeword_buffer_added') {
                            console.log('Pre-wake-word buffer successfully added to conversation');
                            this.realtimeWebsocket.onmessage = originalHandler;
                            resolve();
                        } else {
                            // Handle other messages normally
                            originalHandler(event);
                        }
                    };
                });
            } else {
                console.log('No pre-wake-word buffer available');
            }

            // Start streaming audio
            await this.startStreamingAudio();

            console.log('Real-time streaming started successfully');

        } catch (error) {
            console.error('Error starting real-time streaming:', error);
            this.showError('Failed to start real-time streaming');
            this.updateWakeWordStatus('Listening for "Scarlett"...');
        }
    }



    updateWakeWordStatus(message) {
        // Update the wake word indicator
        this.wakeWordStatus.textContent = message;
        
        // Update indicator classes based on status
        if (message.includes('offline')) {
            this.wakeWordIndicator.classList.add('offline');
            this.wakeWordIndicator.classList.remove('active');
        } else if (message.includes('detected')) {
            this.wakeWordIndicator.classList.add('active');
            this.wakeWordIndicator.classList.remove('offline');
        } else {
            this.wakeWordIndicator.classList.remove('offline', 'active');
        }
    }
    
    async toggleWakeWordListening() {
        if (!this.wakeWordEnabled) {
            // Enable wake word listening
            this.wakeWordEnabled = true;
            this.wakeWordBtn.textContent = 'Stop Listening';
            this.wakeWordBtn.classList.add('active');

            try {
                // Show the wake word indicator
                this.wakeWordIndicator.style.display = 'flex';

                // Wait for continuous recording to be ready (with timeout)
                let waitCount = 0;
                while (!this.stream && waitCount < 50) { // 5 second timeout
                    console.log('Waiting for microphone stream to be ready...');
                    await new Promise(resolve => setTimeout(resolve, 100));
                    waitCount++;
                }

                if (!this.stream) {
                    throw new Error('Microphone stream not available after timeout');
                }

                // Initialize wake word detection
                await this.initializeWakeWordDetection();
                
            } catch (error) {
                console.error('Error starting wake word detection:', error);
                this.showError('Failed to start wake word detection');
                // Revert state on error
                this.wakeWordEnabled = false;
                this.wakeWordBtn.textContent = 'Start Listening';
                this.wakeWordBtn.classList.remove('active');
                this.wakeWordIndicator.style.display = 'none';
            }
        } else {
            // Disable wake word listening
            this.wakeWordEnabled = false;
            this.wakeWordBtn.textContent = 'Start Listening';
            this.wakeWordBtn.classList.remove('active');
            
            // Hide the wake word indicator
            this.wakeWordIndicator.style.display = 'none';
            
            // Clean up resources
            if (this.websocket) {
                this.websocket.close();
                this.websocket = null;
            }
            
            if (this.scriptProcessor) {
                this.scriptProcessor.disconnect();
                this.scriptProcessor = null;
            }
            
            if (this.audioContext && this.audioContext.state !== 'closed') {
                this.audioContext.close();
                this.audioContext = null;
            }
        }
    }

    // Real-time streaming methods
    async initializeRealtimeStreaming() {
        try {
            // Connect to realtime WebSocket
            this.realtimeWebsocket = new WebSocket('ws://localhost:8000/ws/realtime');

            this.realtimeWebsocket.onopen = () => {
                console.log('Realtime WebSocket connected');

                // Start streaming session
                this.realtimeWebsocket.send(JSON.stringify({
                    type: 'start_session'
                }));
            };

            this.realtimeWebsocket.onmessage = (event) => {
                const message = JSON.parse(event.data);
                this.handleRealtimeMessage(message);
            };

            this.realtimeWebsocket.onclose = () => {
                console.log('Realtime WebSocket disconnected');
                this.stopStreamingAudio();
                this.isStreaming = false;
                this.updateWakeWordStatus('Listening for "Scarlett"...');
            };

            this.realtimeWebsocket.onerror = (error) => {
                console.error('Realtime WebSocket error:', error);
                this.stopStreamingAudio();
                this.isStreaming = false;
                this.updateWakeWordStatus('Listening for "Scarlett"...');
            };

            // Initialize audio context for streaming - use 24kHz to match backend/playback
            this.streamingAudioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: 24000
            });

            if (this.streamingAudioContext.state === 'suspended') {
                await this.streamingAudioContext.resume();
            }

        } catch (error) {
            console.error('Error initializing realtime streaming:', error);
            throw error;
        }
    }

    handleRealtimeMessage(message) {
        switch (message.type) {
            case 'session_started':
                console.log('Realtime session started');
                this.isStreaming = true;
                this.responseComplete = false;
                this.shouldStopAfterAudio = false;
                this.isPlayingAudio = false;
                this.nextStartTime = 0;
                this.activeSources = [];
                // Clear previous transcripts
                this.userTranscriptDiv.textContent = '';
                this.transcriptDiv.textContent = '';
                this.userTranscriptDiv.style.display = 'none';
                this.transcriptDiv.style.display = 'none';
                this.updateWakeWordStatus('Listening... (automatic turn detection enabled)');
                break;

            case 'audio_chunk':
                // Queue audio chunk for playback
                this.audioPlaybackQueue.push(message.data);
                this.processAudioQueue();
                break;

            case 'transcript_event':
                // Handle transcript events
                this.handleTranscriptEvent(message.event);
                break;

            case 'response_complete':
                console.log('Response complete - continuing to listen for interruptions');
                this.responseComplete = true;
                this.shouldStopAfterAudio = true; 
                break;

            case 'session_stopped':
                console.log('Realtime session stopped');
                this.isStreaming = false;
                this.updateWakeWordStatus('Listening for "Scarlett"...');
                break;

            case 'tool_processing_complete':
                console.log('Tool processing complete:', message.message);
                // Tool calls have been processed, continue normal operation
                break;

            case 'error':
                console.error('Realtime API error:', message.message);
                this.showError(`Realtime error: ${message.message}`);
                this.stopStreamingAudio();
                this.isStreaming = false;
                this.updateWakeWordStatus('Listening for "Scarlett"...');
                break;
        }
    }

    handleTranscriptEvent(event) {
        switch (event.type) {
            case 'user_transcript':
                // Show user transcript
                this.showUserTranscript(event.transcript);
                break;

            case 'assistant_transcript_delta':
                // Update assistant transcript with streaming text
                this.updateAssistantTranscript(event.delta, event.full_transcript);
                break;

            case 'assistant_transcript_done':
                // Finalize assistant transcript
                this.finalizeAssistantTranscript(event.transcript);
                break;

            case 'speech_started_interruption':
                // User started speaking - handle interruption
                console.log('Interruption detected - stopping audio playback');
                this.handleInterruption();
                break;
        }
    }

    showUserTranscript(transcript) {
        if (transcript && transcript.trim()) {
            this.userTranscriptDiv.textContent = transcript;
            this.userTranscriptDiv.style.display = 'block';
            console.log('User said:', transcript);
        }
    }

    updateAssistantTranscript(delta, fullTranscript) {
        if (delta) {
            // Show the AI response section if not already visible
            this.transcriptDiv.style.display = 'block';
            // Update with the full transcript so far
            this.transcriptDiv.textContent = fullTranscript;
        }
    }

    finalizeAssistantTranscript(transcript) {
        if (transcript && transcript.trim()) {
            this.transcriptDiv.textContent = transcript;
            console.log('Assistant said:', transcript);
        }
    }

    handleInterruption() {
        // Stop all current audio playback immediately
        console.log('Handling interruption - stopping audio playback');

        // Clear the audio queue
        this.audioPlaybackQueue = [];

        // Stop all active audio sources
        this.activeSources.forEach(source => {
            try {
                source.stop();
            } catch (e) {
                // Source might already be stopped
            }
        });
        this.activeSources = [];

        // Reset playback state
        this.isPlayingAudio = false;
        this.nextStartTime = 0;

        // Update UI to show interruption
        this.updateWakeWordStatus('Listening... (interrupted)');
    }

    checkIfShouldStop() {
        // If response is complete and no audio is playing and queue is empty, stop streaming
        if (this.shouldStopAfterAudio && !this.isPlayingAudio && this.audioPlaybackQueue.length === 0) {
            console.log('Audio playback finished - returning to wake word listening');
            this.stopStreamingAudio();
            this.isStreaming = false;
            this.responseComplete = false;
            this.shouldStopAfterAudio = false;
            this.updateWakeWordStatus('Listening for "Scarlett"...');
        }
    }

    async processAudioQueue() {
        // If no chunks to process, return
        if (this.audioPlaybackQueue.length === 0) return;

        try {
            // Initialize audio context for playback if not already done
            if (!this.audioPlaybackContext) {
                this.audioPlaybackContext = new (window.AudioContext || window.webkitAudioContext)({
                    sampleRate: 24000
                });

                if (this.audioPlaybackContext.state === 'suspended') {
                    await this.audioPlaybackContext.resume();
                }

                // Initialize timing for seamless playback
                this.nextStartTime = this.audioPlaybackContext.currentTime;
            }

            // Process all queued chunks with scheduled playback
            while (this.audioPlaybackQueue.length > 0) {
                const audioChunk = this.audioPlaybackQueue.shift();
                this.scheduleAudioChunk(audioChunk);
            }

        } catch (error) {
            console.error('Error processing audio queue:', error);
        }
    }

    scheduleAudioChunk(audioChunk) {
        try {
            // Convert base64 PCM16 to playable audio
            const audioBytes = atob(audioChunk);
            const pcm16Array = new Int16Array(audioBytes.length / 2);

            for (let i = 0; i < pcm16Array.length; i++) {
                const byte1 = audioBytes.charCodeAt(i * 2);
                const byte2 = audioBytes.charCodeAt(i * 2 + 1);
                pcm16Array[i] = (byte2 << 8) | byte1;
            }

            // Create audio buffer
            const audioBuffer = this.audioPlaybackContext.createBuffer(1, pcm16Array.length, 24000);
            const channelData = audioBuffer.getChannelData(0);

            // Convert Int16 to Float32 for Web Audio API
            for (let i = 0; i < pcm16Array.length; i++) {
                channelData[i] = pcm16Array[i] / 32768.0; // Convert to -1.0 to 1.0 range
            }

            // Create and schedule audio source for seamless playback
            const source = this.audioPlaybackContext.createBufferSource();
            source.buffer = audioBuffer;
            source.connect(this.audioPlaybackContext.destination);

            // Calculate when this chunk should start (seamlessly after the previous one)
            const currentTime = this.audioPlaybackContext.currentTime;
            const startTime = Math.max(currentTime, this.nextStartTime);

            // Schedule the chunk to start at the precise time
            source.start(startTime);

            // Update next start time for seamless continuation
            this.nextStartTime = startTime + audioBuffer.duration;

            // Track active sources for cleanup
            this.activeSources.push(source);

            // Set up playback tracking
            if (!this.isPlayingAudio) {
                this.isPlayingAudio = true;
            }

            // Clean up when this chunk ends
            source.onended = () => {
                // Remove from active sources
                const index = this.activeSources.indexOf(source);
                if (index > -1) {
                    this.activeSources.splice(index, 1);
                }

                // Check if all audio has finished
                if (this.activeSources.length === 0 && this.audioPlaybackQueue.length === 0) {
                    this.isPlayingAudio = false;
                    this.checkIfShouldStop();
                }
            };

        } catch (error) {
            console.error('Error scheduling audio chunk:', error);
        }
    }

    async startStreamingAudio() {
        try {
            // Reuse the existing microphone stream instead of requesting new permissions
            if (!this.stream) {
                throw new Error('No microphone stream available');
            }

            // Create audio processing pipeline for streaming using existing stream
            this.streamingSource = this.streamingAudioContext.createMediaStreamSource(this.stream);
            this.streamingProcessor = this.streamingAudioContext.createScriptProcessor(1024, 1, 1);

            this.streamingProcessor.onaudioprocess = (event) => {
                if (!this.isStreaming || !this.realtimeWebsocket || this.realtimeWebsocket.readyState !== WebSocket.OPEN) {
                    return;
                }

                // Get audio data
                const inputData = event.inputBuffer.getChannelData(0);

                // Create WAV chunk from audio data
                const wavBlob = this.createWavFromRawData(inputData, this.streamingAudioContext.sampleRate);

                // Convert to base64 and send
                const reader = new FileReader();
                reader.onload = () => {
                    const base64Data = reader.result.split(',')[1]; // Remove data:audio/wav;base64, prefix

                    try {
                        if (this.realtimeWebsocket && this.realtimeWebsocket.readyState === WebSocket.OPEN) {
                            this.realtimeWebsocket.send(JSON.stringify({
                                type: 'audio_chunk',
                                data: base64Data
                            }));
                        }
                    } catch (error) {
                        console.error('Error sending audio chunk:', error);
                        this.stopStreamingAudio();
                        this.isStreaming = false;
                        this.updateWakeWordStatus('Listening for "Scarlett"...');
                    }
                };
                reader.readAsDataURL(wavBlob);
            };

            // Connect audio nodes
            this.streamingSource.connect(this.streamingProcessor);
            this.streamingProcessor.connect(this.streamingAudioContext.destination);

        } catch (error) {
            console.error('Error starting streaming audio:', error);
            throw error;
        }
    }

    stopStreamingAudio() {
        try {
            if (this.streamingProcessor) {
                this.streamingProcessor.disconnect();
                this.streamingProcessor = null;
            }

            if (this.streamingSource) {
                this.streamingSource.disconnect();
                this.streamingSource = null;
            }

            // Stop any active audio sources
            this.activeSources.forEach(source => {
                try {
                    source.stop();
                } catch (e) {
                    // Source may already be stopped
                }
            });
            this.activeSources = [];
            this.isPlayingAudio = false;

            // No need to manually commit audio - the API handles turn detection automatically
            if (this.realtimeWebsocket && this.realtimeWebsocket.readyState === WebSocket.OPEN) {
                try {
                    this.realtimeWebsocket.send(JSON.stringify({
                        type: 'stop_session'
                    }));
                } catch (error) {
                    console.error('Error stopping session:', error);
                }
            }
        } catch (error) {
            console.error('Error in stopStreamingAudio:', error);
        }
    }

    // Clean up method for when the page is unloaded
    cleanup() {
        this.isRecording = false;

        // Stop typing sound during cleanup
        this.stopTypingSound();

        if (this.bufferProcessor) {
            this.bufferProcessor.disconnect();
        }

        if (this.bufferAudioContext && this.bufferAudioContext.state !== 'closed') {
            this.bufferAudioContext.close();
        }

        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
        }

        if (this.websocket) {
            this.websocket.close();
        }

        if (this.audioContext && this.audioContext.state !== 'closed') {
            this.audioContext.close();
        }
    }
}

// Initialize the recorder when DOM is loaded
let recorder;
document.addEventListener('DOMContentLoaded', () => {
    recorder = new AudioRecorder();
});

// Clean up when page is unloaded
window.addEventListener('beforeunload', () => {
    if (recorder) {
        recorder.cleanup();
    }
});

// Clean up when page is unloaded
window.addEventListener('beforeunload', () => {
    if (recorder) {
        recorder.cleanup();
    }
});
